import React, { useCallback, useMemo, useState } from 'react';
import { groupBy, isEmpty, isNumber, some, toLower, trim } from 'lodash';

import { IEContentLibraryForm } from '../../EContentLibraryForm';
import useT from '../../../../../../../common/components/utils/Translations/useT';
import EntityForm from '../../../../../../../common/components/containers/EntityForm';
import EntityFormFieldSet from '../../../../../../../common/components/containers/EntityForm/EntityFormFieldSet';
import EntityNameField from '../../../../../../../common/components/containers/EntityForm/fields/EntityNameField';
import StatusWithDraftField from '../../../../../../../common/components/containers/EntityForm/fields/StatusWithDraftField';
import TextAreaField from '../../../../../../../common/components/containers/EntityForm/fields/TextAreaField';
import CheckboxWithConfirmField from '../../../../../../../common/components/containers/EntityForm/fields/CheckboxWithConfirmField';
import styles from './EContentLibraryDetailsTab.scss';
import DependsOnField from '../../../../../../../common/components/containers/EntityForm/DependsOnField';
import EcnRolesField from './EcnRolesField';
import { Active } from '../../../../../../../model/Statuses';
import Notifications from '../../../../../../../common/utils/Notifications';

const EContentLibraryDetailsTab: React.FC<IEContentLibraryForm> = props => {
  const { entity, onGoBack, onSubmit } = props;

  const isNew = useMemo(() => !entity?.id, [entity]);

  const t = useT();

  const hasDuplicateNames = useCallback(ecnRoles => {
    const activeRoles = ecnRoles.filter(x => x.status === Active.value);
    const grouped = groupBy(activeRoles, item => toLower(trim(item.name)));
    return some(grouped, group => group.length > 1);
  }, []);

  const _onSubmit = useCallback(
    ({
      id,
      organisationGroupId,
      name,
      description,
      status,
      hasProcessManagement,
      ecnRoles,
    }) => {
      const _ecnRoles = ecnRoles
        .filter(x => !isEmpty(x.name))
        .map(y => ({
          ...y,
          id: isNumber(y.id) ? y.id : null,
        }));

      if (hasDuplicateNames(_ecnRoles)) {
        Notifications.error('Error', t('Role should be unique'), t);
        return false;
      }
      return onSubmit({
        id,
        organisationGroupId,
        name,
        description,
        status,
        hasProcessManagement,
        ecnRoles: _ecnRoles,
      });
    },
    [onSubmit, hasDuplicateNames, t],
  );

  const setFieldToEmpty = useCallback(
    fieldName => ({ setFieldValue }) => setFieldValue(fieldName, []),
    [],
  );

  const condition = useCallback(({ values }) => {
    const _roles = values.ecnRoles?.filter(x => !isEmpty(x.name)) || [];
    return !isEmpty(_roles);
  }, []);

  return (
    <EntityForm
      entity={entity}
      hasValidateOnBlur={false}
      title={t('Details')}
      validateOnMount={false}
      onCancel={isNew ? onGoBack : undefined}
      onGoBack={isNew ? undefined : onGoBack}
      onSubmit={_onSubmit}
    >
      <EntityFormFieldSet>
        <EntityNameField columns={4} label={t('Library Name')} />
        <StatusWithDraftField columns={4} />
      </EntityFormFieldSet>
      <EntityFormFieldSet>
        <TextAreaField
          columns={1}
          label={t('Description')}
          name="description"
        />
      </EntityFormFieldSet>
      <EntityFormFieldSet>
        <CheckboxWithConfirmField
          className={styles.checkbox}
          columns={1}
          condition={condition}
          label={t('Process Management')}
          name="hasProcessManagement"
          popupText={t('Added role(s) will be removed, proceed?')}
          onUncheck={setFieldToEmpty('ecnRoles')}
        />
      </EntityFormFieldSet>
      <DependsOnField fieldName="hasProcessManagement">
        {hasProcessManagement =>
          hasProcessManagement ? <EcnRolesField /> : null
        }
      </DependsOnField>
    </EntityForm>
  );
};

export default EContentLibraryDetailsTab;
